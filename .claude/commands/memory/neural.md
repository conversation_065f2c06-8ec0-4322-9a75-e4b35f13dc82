# Neural Pattern Training

## 🎯 Key Principle
**This tool coordinates <PERSON>'s actions. It does NOT write code or create content.**

## MCP Tool Usage in Claude Code

**Tool:** `mcp__ruv-swarm__neural_train`

## Parameters
```json
{"iterations": 10}
```

## Description
Improve coordination patterns through neural network training

## Details
Training improves:
- Task breakdown effectiveness
- Coordination pattern selection
- Resource allocation strategies
- Overall coordination efficiency

## Example Usage

**In Claude Code:**
1. Use the tool: `mcp__ruv-swarm__neural_train`
2. With parameters: `{"iterations": 10}`
3. <PERSON> Code then executes the coordinated plan using its native tools

## Important Reminders
- ✅ This tool provides coordination and structure
- ✅ Claude Code performs all actual implementation
- ❌ The tool does NOT write code
- ❌ The tool does NOT access files directly
- ❌ The tool does NOT execute commands

## See Also
- Main documentation: /claude.md
- Other commands in this category
- Workflow examples in /workflows/
