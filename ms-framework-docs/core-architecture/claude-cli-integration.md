# Claude CLI Integration Architecture

## Core Component Specifications for Mister Smith Framework

### Overview

This document specifies the core architecture for integrating Claude Code CLI capabilities into the Mister Smith multi-agent framework. The integration adds new components while preserving existing framework architecture and patterns.

---

## 🔍 VALIDATION STATUS

**Last Validated**: 2025-07-05  
**Validator**: Framework Documentation Team  
**Validation Score**: Pending full validation  
**Status**: Active Development  

### Implementation Status

- Claude CLI controller architecture defined
- Hook bridge patterns established
- Session management framework complete
- Integration patterns documented

---

## Component Architecture

### 1. Claude CLI Controller

**Purpose**: Central management component for Claude CLI instance lifecycle, resource allocation, and coordination.

**Location**: `src/claude_cli/controller.rs`

```rust
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use async_nats::Client as NatsClient;

pub struct ClaudeCliController {
    config: ClaudeCliConfig,
    active_sessions: Arc<RwLock<HashMap<AgentId, ClaudeSession>>>,
    agent_pool: AgentPool,
    nats_client: NatsClient,
    hook_bridge: Arc<HookBridge>,
    metrics: ControllerMetrics,
    resource_manager: ResourceManager,
}

impl ClaudeCliController {
    pub async fn new(config: ClaudeCliConfig, nats_client: NatsClient) -> Result<Self, ControllerError> {
        let hook_bridge = Arc::new(HookBridge::new(nats_client.clone()).await?);
        let agent_pool = AgentPool::new(config.max_concurrent_agents);
        let resource_manager = ResourceManager::new(&config);
        
        Ok(Self {
            config,
            active_sessions: Arc::new(RwLock::new(HashMap::new())),
            agent_pool,
            nats_client,
            hook_bridge,
            metrics: ControllerMetrics::new(),
            resource_manager,
        })
    }
    
    pub async fn spawn_agent(&self, request: SpawnRequest) -> Result<AgentId, SpawnError> {
        // Validate resource availability
        self.resource_manager.validate_spawn_request(&request).await?;
        
        // Build Claude CLI command
        let command = self.build_claude_command(&request)?;
        
        // Spawn supervised process
        let session = ClaudeSession::spawn(
            command,
            self.nats_client.clone(),
            self.hook_bridge.clone()
        ).await?;
        
        // Register with agent pool
        let agent_id = self.agent_pool.register(session.clone()).await?;
        
        // Store active session
        self.active_sessions.write().await.insert(agent_id.clone(), session);
        
        // Update metrics
        self.metrics.increment_active_agents();
        
        Ok(agent_id)
    }
    
    pub async fn terminate_agent(&self, agent_id: &AgentId) -> Result<(), TerminateError> {
        let session = {
            self.active_sessions.write().await.remove(agent_id)
        };
        
        if let Some(session) = session {
            // Graceful shutdown with timeout
            session.terminate_gracefully(Duration::from_secs(30)).await?;
            
            // Unregister from pool
            self.agent_pool.unregister(agent_id).await?;
            
            // Update metrics
            self.metrics.decrement_active_agents();
        }
        
        Ok(())
    }
    
    pub async fn get_agent_status(&self, agent_id: &AgentId) -> Option<AgentStatus> {
        let sessions = self.active_sessions.read().await;
        sessions.get(agent_id).map(|session| session.status())
    }
    
    pub async fn list_active_agents(&self) -> Vec<AgentInfo> {
        let sessions = self.active_sessions.read().await;
        sessions.iter()
            .map(|(id, session)| AgentInfo {
                id: id.clone(),
                status: session.status(),
                uptime: session.uptime(),
                resource_usage: session.resource_usage(),
            })
            .collect()
    }
    
    fn build_claude_command(&self, request: &SpawnRequest) -> Result<ClaudeCommand, CommandError> {
        let mut command = ClaudeCommand::new();
        
        // Set model
        command.model(&request.model.as_ref().unwrap_or(&self.config.default_model));
        
        // Set output format
        command.output_format(OutputFormat::StreamJson);
        
        // Set tool restrictions
        if let Some(allowed_tools) = &request.allowed_tools {
            command.allowed_tools(allowed_tools);
        }
        
        // Set MCP configuration
        if self.config.mcp.enable_mcp_integration {
            command.mcp_config(&self.config.mcp.mcp_config_path);
        }
        
        // Set hook configuration
        command.hooks_config(&self.config.hooks.config_path);
        
        // Set session limits
        if let Some(max_turns) = request.max_turns {
            command.max_turns(max_turns);
        }
        
        Ok(command)
    }
}
```

### 2. Claude Session Management

**Purpose**: Individual Claude CLI process management with supervision and monitoring.

```rust
pub struct ClaudeSession {
    agent_id: AgentId,
    process: Arc<Mutex<Child>>,
    stdin: Arc<Mutex<ChildStdin>>,
    stdout_reader: Arc<Mutex<BufReader<ChildStdout>>>,
    status: Arc<RwLock<SessionStatus>>,
    start_time: Instant,
    nats_client: NatsClient,
    hook_bridge: Arc<HookBridge>,
    output_parser: TaskOutputParser,
    metrics: SessionMetrics,
}

impl ClaudeSession {
    pub async fn spawn(
        command: ClaudeCommand,
        nats_client: NatsClient,
        hook_bridge: Arc<HookBridge>
    ) -> Result<Self, SpawnError> {
        // Spawn Claude CLI process
        let mut child = command.spawn()?;
        
        // Extract stdio handles
        let stdin = child.stdin.take().ok_or(SpawnError::NoStdin)?;
        let stdout = child.stdout.take().ok_or(SpawnError::NoStdout)?;
        
        // Generate agent ID
        let agent_id = AgentId::new();
        
        // Create session
        let session = Self {
            agent_id: agent_id.clone(),
            process: Arc::new(Mutex::new(child)),
            stdin: Arc::new(Mutex::new(stdin)),
            stdout_reader: Arc::new(Mutex::new(BufReader::new(stdout))),
            status: Arc::new(RwLock::new(SessionStatus::Starting)),
            start_time: Instant::now(),
            nats_client: nats_client.clone(),
            hook_bridge,
            output_parser: TaskOutputParser::new(nats_client),
            metrics: SessionMetrics::new(),
        };
        
        // Start output processing task
        session.start_output_processing().await?;
        
        // Update status
        *session.status.write().await = SessionStatus::Active;
        
        Ok(session)
    }
    
    async fn start_output_processing(&self) -> Result<(), ProcessingError> {
        let stdout_reader = self.stdout_reader.clone();
        let output_parser = self.output_parser.clone();
        let agent_id = self.agent_id.clone();
        let status = self.status.clone();
        
        tokio::spawn(async move {
            let mut reader = stdout_reader.lock().await;
            let mut line = String::new();
            
            loop {
                line.clear();
                match reader.read_line(&mut line).await {
                    Ok(0) => break, // EOF
                    Ok(_) => {
                        // Process output line
                        if let Err(e) = output_parser.process_line(&line, &agent_id).await {
                            eprintln!("Output processing error: {}", e);
                        }
                    }
                    Err(e) => {
                        eprintln!("Read error: {}", e);
                        *status.write().await = SessionStatus::Error(e.to_string());
                        break;
                    }
                }
            }
            
            *status.write().await = SessionStatus::Terminated;
        });
        
        Ok(())
    }
    
    pub async fn send_input(&self, input: &str) -> Result<(), InputError> {
        let mut stdin = self.stdin.lock().await;
        stdin.write_all(input.as_bytes()).await?;
        stdin.write_all(b"\n").await?;
        stdin.flush().await?;
        Ok(())
    }
    
    pub async fn terminate_gracefully(&self, timeout: Duration) -> Result<(), TerminateError> {
        // Send termination signal
        self.send_input("exit").await.ok();
        
        // Wait for graceful shutdown
        let start = Instant::now();
        while start.elapsed() < timeout {
            if matches!(*self.status.read().await, SessionStatus::Terminated) {
                return Ok(());
            }
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
        
        // Force termination if graceful shutdown failed
        let mut process = self.process.lock().await;
        process.kill().await?;
        
        Ok(())
    }
    
    pub fn status(&self) -> SessionStatus {
        // Non-blocking status read
        self.status.try_read()
            .map(|status| status.clone())
            .unwrap_or(SessionStatus::Unknown)
    }
    
    pub fn uptime(&self) -> Duration {
        self.start_time.elapsed()
    }
    
    pub fn resource_usage(&self) -> ResourceUsage {
        self.metrics.current_usage()
    }
}
```

### 3. Agent Pool Management

**Purpose**: Resource-bounded pool management for concurrent Claude CLI agents.

```rust
pub struct AgentPool {
    max_size: usize,
    active_agents: Arc<RwLock<HashMap<AgentId, AgentPoolEntry>>>,
    spawn_semaphore: Arc<Semaphore>,
    metrics: PoolMetrics,
}

impl AgentPool {
    pub fn new(max_size: usize) -> Self {
        Self {
            max_size,
            active_agents: Arc::new(RwLock::new(HashMap::new())),
            spawn_semaphore: Arc::new(Semaphore::new(max_size)),
            metrics: PoolMetrics::new(),
        }
    }
    
    pub async fn register(&self, session: ClaudeSession) -> Result<AgentId, PoolError> {
        // Acquire spawn permit
        let _permit = self.spawn_semaphore.acquire().await?;
        
        let agent_id = session.agent_id.clone();
        let entry = AgentPoolEntry {
            session,
            registered_at: Instant::now(),
            _permit: _permit,
        };
        
        // Register in pool
        self.active_agents.write().await.insert(agent_id.clone(), entry);
        
        // Update metrics
        self.metrics.increment_active_count();
        
        Ok(agent_id)
    }
    
    pub async fn unregister(&self, agent_id: &AgentId) -> Result<(), PoolError> {
        let entry = self.active_agents.write().await.remove(agent_id);
        
        if entry.is_some() {
            // Permit is automatically released when entry is dropped
            self.metrics.decrement_active_count();
        }
        
        Ok(())
    }
    
    pub async fn get_pool_status(&self) -> PoolStatus {
        let active_agents = self.active_agents.read().await;
        
        PoolStatus {
            active_count: active_agents.len(),
            max_capacity: self.max_size,
            available_slots: self.spawn_semaphore.available_permits(),
            agents: active_agents.keys().cloned().collect(),
        }
    }
}

struct AgentPoolEntry {
    session: ClaudeSession,
    registered_at: Instant,
    _permit: SemaphorePermit<'static>,
}
```

---

## Configuration Schema

### Claude CLI Configuration

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClaudeCliConfig {
    pub max_concurrent_agents: usize,
    pub default_model: String,
    pub api_timeout: u64,
    pub retry_attempts: u32,
    pub hook_timeout: u64,
    pub output_buffer_size: usize,
    pub spawn_limits: SpawnLimits,
    pub hooks: HooksConfig,
    pub mcp: McpConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpawnLimits {
    pub rate_limit_per_minute: u32,
    pub memory_limit_mb: u64,
    pub cpu_limit_percent: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HooksConfig {
    pub config_path: PathBuf,
    pub enable_nats_bridge: bool,
    pub hook_execution_timeout: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct McpConfig {
    pub enable_mcp_integration: bool,
    pub mcp_config_path: PathBuf,
}
```

---

## Integration Points

### 1. Framework Integration

**Supervisor Tree Integration**:

```rust
// Add to existing supervision tree
impl SupervisionTree {
    pub async fn add_claude_cli_controller(&mut self, controller: ClaudeCliController) -> Result<(), SupervisionError> {
        let supervisor = ClaudeCliSupervisor::new(controller);
        self.add_supervisor("claude_cli", Box::new(supervisor)).await
    }
}
```

**NATS Integration**:

```rust
// Hook bridge publishes to existing NATS subjects
impl HookBridge {
    async fn publish_hook_event(&self, event: HookEvent) -> Result<(), NatsError> {
        let subject = match event.hook_type {
            HookType::Startup => "control.startup",
            HookType::PreTask => &format!("agent.{}.pre", event.agent_id),
            HookType::PostTask => &format!("agent.{}.post", event.agent_id),
            HookType::OnError => &format!("agent.{}.error", event.agent_id),
            HookType::OnFileChange => &format!("ctx.{}.file_change", event.context_id),
        };
        
        self.nats_client.publish(subject, event.to_json()).await
    }
}
```

### 2. Resource Management

**Memory Management**:

- Per-agent memory limits enforced by resource manager
- Shared memory for common data (CLAUDE.md files)
- Garbage collection coordination

**Process Management**:

- Tokio supervision patterns for fault tolerance
- Graceful shutdown with configurable timeouts
- Process health monitoring and restart policies

This architecture provides a robust foundation for Claude CLI integration while maintaining compatibility with existing framework components and patterns.
