# ci-cd-pipeline.yml - Complete CI/CD pipeline for MS Framework
# This is an enhanced version of the pipeline in build-specifications.md
# with additional features for comprehensive automation

name: MS Framework CI/CD Pipeline

on:
  push:
    branches: [main, develop, 'release/*']
    tags: ['v*']
  pull_request:
    branches: [main, develop]
  schedule:
    - cron: '0 0 * * 1'  # Weekly builds on Monday
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release type (major/minor/patch/prerelease)'
        required: false
        default: 'patch'

env:
  RUST_BACKTRACE: 1
  CARGO_TERM_COLOR: always
  RUSTFLAGS: "-D warnings"
  SCCACHE_GHA_ENABLED: "true"
  CARGO_INCREMENTAL: 0  # Disable for CI builds

jobs:
  # ============================================================================
  # SECURITY AND COMPLIANCE CHECKS
  # ============================================================================
  
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
      
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results.sarif'
      
      - name: Dependency audit
        uses: actions-rs/audit-check@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: License scan
        uses: fossas/fossa-action@v1
        with:
          api-key: ${{ secrets.FOSSA_API_KEY }}

  # ============================================================================
  # CODE QUALITY CHECKS
  # ============================================================================
  
  code-quality:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy
      
      - uses: mozilla-actions/sccache-action@v0.0.3
      
      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: "quality"
      
      - name: Check formatting
        run: cargo fmt -- --check
      
      - name: Clippy analysis
        run: |
          cargo clippy --all-targets --all-features -- -D warnings \
            -W clippy::all \
            -W clippy::pedantic \
            -W clippy::nursery \
            -A clippy::module_name_repetitions \
            -A clippy::must_use_candidate
      
      - name: Check documentation
        run: |
          cargo doc --all-features --no-deps --document-private-items
          cargo test --doc --all-features
      
      - name: Verify no unsafe code
        run: |
          cargo install --locked cargo-geiger || true
          cargo geiger --all-features

  # ============================================================================
  # TESTING MATRIX
  # ============================================================================
  
  test-matrix:
    name: Test Suite
    needs: [security-scan, code-quality]
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        rust: [stable, nightly]
        features: [default, tier_1, tier_2, tier_3]
        include:
          - os: ubuntu-latest
            rust: stable
            features: tier_3
            coverage: true
    
    runs-on: ${{ matrix.os }}
    
    steps:
      - uses: actions/checkout@v4
      
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ matrix.rust }}
          components: llvm-tools-preview
      
      - uses: mozilla-actions/sccache-action@v0.0.3
      
      - name: Install cargo-nextest
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-nextest
      
      - name: Install cargo-llvm-cov (coverage)
        if: matrix.coverage
        uses: taiki-e/install-action@v2
        with:
          tool: cargo-llvm-cov
      
      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: "test-${{ matrix.os }}-${{ matrix.rust }}"
      
      - name: Run tests
        run: |
          cargo nextest run --all-features --profile ci
          cargo test --doc --all-features
      
      - name: Run integration tests
        if: matrix.os == 'ubuntu-latest'
        run: |
          cargo nextest run --test '*integration*' --features ${{ matrix.features }}
      
      - name: Generate coverage report
        if: matrix.coverage
        run: |
          cargo llvm-cov nextest --all-features --lcov --output-path lcov.info
          cargo llvm-cov report --all-features
      
      - name: Upload coverage
        if: matrix.coverage
        uses: codecov/codecov-action@v3
        with:
          files: lcov.info
          fail_ci_if_error: true

  # ============================================================================
  # PERFORMANCE TESTING
  # ============================================================================
  
  performance-tests:
    name: Performance Tests
    needs: [test-matrix]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'schedule'
    
    steps:
      - uses: actions/checkout@v4
      
      - uses: dtolnay/rust-toolchain@stable
      
      - uses: mozilla-actions/sccache-action@v0.0.3
      
      - name: Install criterion tools
        run: |
          cargo install cargo-criterion --locked || true
          cargo install critcmp --locked || true
      
      - name: Cache benchmark data
        uses: actions/cache@v4
        with:
          path: target/criterion
          key: benchmarks-${{ runner.os }}-${{ github.sha }}
          restore-keys: |
            benchmarks-${{ runner.os }}-
      
      - name: Run benchmarks
        run: |
          cargo bench --all-features -- --save-baseline current
          
          # Compare with previous if exists
          if [ -d "target/criterion/previous" ]; then
            critcmp previous current
          fi
          
          # Save for next run
          mv target/criterion/current target/criterion/previous
      
      - name: Upload benchmark results
        uses: actions/upload-artifact@v4
        with:
          name: benchmark-results
          path: target/criterion

  # ============================================================================
  # BUILD MATRIX
  # ============================================================================
  
  build-matrix:
    name: Cross-Platform Build
    needs: [test-matrix]
    strategy:
      fail-fast: false
      matrix:
        include:
          # Linux builds
          - target: x86_64-unknown-linux-gnu
            os: ubuntu-latest
            features: tier_3
            
          - target: x86_64-unknown-linux-musl
            os: ubuntu-latest
            features: tier_3
            use-cross: true
            
          - target: aarch64-unknown-linux-gnu
            os: ubuntu-latest
            features: tier_3
            use-cross: true
            
          - target: armv7-unknown-linux-gnueabihf
            os: ubuntu-latest
            features: tier_2
            use-cross: true
            
          # macOS builds
          - target: x86_64-apple-darwin
            os: macos-latest
            features: tier_3
            
          - target: aarch64-apple-darwin
            os: macos-latest
            features: tier_3
            
          # Windows builds
          - target: x86_64-pc-windows-msvc
            os: windows-latest
            features: tier_3
            
          - target: x86_64-pc-windows-gnu
            os: windows-latest
            features: tier_3
            
          # WASM builds
          - target: wasm32-unknown-unknown
            os: ubuntu-latest
            features: wasm
            
          - target: wasm32-wasi
            os: ubuntu-latest
            features: wasm
            install-wasm-tools: true
    
    runs-on: ${{ matrix.os }}
    
    steps:
      - uses: actions/checkout@v4
      
      - uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}
      
      - uses: mozilla-actions/sccache-action@v0.0.3
      
      - name: Install cross
        if: matrix.use-cross
        run: cargo install cross --version 0.2.5 --locked
      
      - name: Install wasm tools
        if: matrix.install-wasm-tools
        run: |
          cargo install wasm-pack --locked
          cargo install wasm-bindgen-cli --locked
      
      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2
        with:
          shared-key: "build-${{ matrix.target }}"
      
      - name: Build
        run: |
          if [[ "${{ matrix.use-cross }}" == "true" ]]; then
            cross build --release --target ${{ matrix.target }} --features ${{ matrix.features }}
          else
            cargo build --release --target ${{ matrix.target }} --features ${{ matrix.features }}
          fi
      
      - name: Post-process WASM
        if: startsWith(matrix.target, 'wasm32')
        run: |
          # Optimize WASM size
          cargo install wasm-opt --locked || true
          find target/${{ matrix.target }}/release -name "*.wasm" -exec wasm-opt -O3 -o {}.opt {} \;
      
      - name: Package artifacts
        shell: bash
        run: |
          mkdir -p dist/${{ matrix.target }}
          
          # Package binary
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            cp target/${{ matrix.target }}/release/*.exe dist/${{ matrix.target }}/ || true
          elif [[ "${{ matrix.target }}" == "wasm32"* ]]; then
            cp target/${{ matrix.target }}/release/*.wasm dist/${{ matrix.target }}/ || true
          else
            cp target/${{ matrix.target }}/release/mister-smith-framework dist/${{ matrix.target }}/ || true
          fi
          
          # Add metadata
          echo "${{ github.sha }}" > dist/${{ matrix.target }}/BUILD_SHA
          echo "${{ matrix.features }}" > dist/${{ matrix.target }}/FEATURES
          
          # Create archive
          cd dist
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            7z a ms-framework-${{ matrix.target }}.zip ${{ matrix.target }}/*
          else
            tar czf ms-framework-${{ matrix.target }}.tar.gz ${{ matrix.target }}/*
          fi
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: ms-framework-${{ matrix.target }}
          path: dist/ms-framework-${{ matrix.target }}.*
          retention-days: 7

  # ============================================================================
  # DOCKER BUILD
  # ============================================================================
  
  docker-build:
    name: Docker Multi-Arch Build
    needs: [build-matrix]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            VERSION=${{ github.ref_name }}
            BUILD_DATE=${{ github.event.repository.updated_at }}
            VCS_REF=${{ github.sha }}

  # ============================================================================
  # RELEASE MANAGEMENT
  # ============================================================================
  
  create-release:
    name: Create Release
    needs: [build-matrix, docker-build]
    if: startsWith(github.ref, 'refs/tags/v')
    runs-on: ubuntu-latest
    permissions:
      contents: write
      packages: write
    
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      
      - name: Generate changelog
        id: changelog
        run: |
          # Extract version from tag
          VERSION=${GITHUB_REF#refs/tags/}
          
          # Generate changelog since last tag
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")
          
          if [ -n "$PREVIOUS_TAG" ]; then
            CHANGELOG=$(git log --pretty=format:"- %s (%h)" $PREVIOUS_TAG..HEAD)
          else
            CHANGELOG=$(git log --pretty=format:"- %s (%h)")
          fi
          
          # Save to file
          cat > RELEASE_NOTES.md << EOF
          ## What's Changed
          
          $CHANGELOG
          
          ## Downloads
          
          See assets below for pre-built binaries for various platforms.
          
          ### Docker
          
          \`\`\`bash
          docker pull ghcr.io/${{ github.repository }}:$VERSION
          \`\`\`
          EOF
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
      
      - name: Create checksums
        run: |
          cd artifacts
          find . -name "*.tar.gz" -o -name "*.zip" | xargs sha256sum > SHA256SUMS
          cd ..
      
      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          files: |
            artifacts/**/*.tar.gz
            artifacts/**/*.zip
            artifacts/SHA256SUMS
          body_path: RELEASE_NOTES.md
          draft: false
          prerelease: ${{ contains(steps.changelog.outputs.version, '-') }}
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # ============================================================================
  # POST-RELEASE ACTIONS
  # ============================================================================
  
  publish-crates:
    name: Publish to crates.io
    needs: [create-release]
    if: startsWith(github.ref, 'refs/tags/v') && !contains(github.ref, '-')
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - uses: dtolnay/rust-toolchain@stable
      
      - name: Publish to crates.io
        run: |
          cargo publish --features tier_3 --no-verify
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}

  # ============================================================================
  # NOTIFICATIONS
  # ============================================================================
  
  notify:
    name: Send Notifications
    needs: [create-release, publish-crates]
    if: always()
    runs-on: ubuntu-latest
    
    steps:
      - name: Send Slack notification
        if: env.SLACK_WEBHOOK_URL != ''
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          text: |
            MS Framework Release ${{ github.ref_name }}
            Status: ${{ job.status }}
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      
      - name: Send Discord notification
        if: env.DISCORD_WEBHOOK_URL != ''
        uses: sarisia/actions-status-discord@v1
        with:
          webhook: ${{ secrets.DISCORD_WEBHOOK_URL }}
          title: "MS Framework Release"
          description: |
            Version: ${{ github.ref_name }}
            Status: ${{ job.status }}