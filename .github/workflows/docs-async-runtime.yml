name: Documentation - Async Runtime & Concurrency

on:
  workflow_dispatch:
    inputs:
      focus_area:
        description: 'Specific area to focus on'
        required: false
        type: choice
        options:
          - 'all'
          - 'tokio-runtime'
          - 'async-patterns'
          - 'supervision-trees'
          - 'agent-lifecycle'
        default: 'all'
  schedule:
    # Run daily at 3 AM UTC
    - cron: '0 3 * * *'
  push:
    paths:
      - 'ms-framework-docs/core-architecture/async-patterns.md'
      - 'ms-framework-docs/core-architecture/tokio-runtime.md'
      - 'ms-framework-docs/core-architecture/supervision-trees.md'
      - 'ms-framework-docs/data-management/agent-lifecycle.md'
      - 'ms-framework-docs/data-management/agent-operations.md'

jobs:
  improve-async-docs:
    name: Improve Async Runtime Documentation
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Setup branch
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          BRANCH_NAME="docs/async-runtime-improvements-$(date +%Y%m%d-%H%M%S)"
          git checkout -b "$BRANCH_NAME"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
      
      - name: Improve Tokio Runtime Documentation (Claude)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'tokio-runtime'
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          model: "claude-sonnet-4-20250514"
          direct_prompt: |
            You are improving the MisterSmith framework documentation for async runtime patterns.
            
            CONTEXT7 LIBRARY REFERENCES:
            - Tokio v1.45.0 (/tokio-rs/tokio) - Core async runtime
            - futures v0.3.31 (/rust-lang/futures-rs) - Async utilities
            - async-trait v0.1.83 (/dtolnay/async-trait) - Async trait support
            - crossbeam-channel v0.5.13 (/crossbeam-rs/crossbeam) - Lock-free channels
            
            TASK: Improve ms-framework-docs/core-architecture/tokio-runtime.md by:
            
            1. Use Context7 to get REAL Tokio v1.45.0 code examples:
               - Search for "runtime configuration" 
               - Search for "task spawning"
               - Search for "channel patterns mpsc broadcast"
               - Search for "select macro"
               - Search for "graceful shutdown"
            
            2. Take the ACTUAL code examples from Context7 and adapt them:
               - Convert real implementations to documentation pseudocode
               - Keep the patterns and structure from real code
               - Replace concrete types with descriptive names
               - Add explanatory comments about what each pattern does
            
            3. Ensure consistency with:
               - ms-framework-docs/core-architecture/async-patterns.md
               - ms-framework-docs/core-architecture/dependency-specifications.md (Tokio 1.45.0)
            
            4. Format according to project standards:
               - Use ```rust blocks for pseudocode
               - Include // comments explaining patterns FROM THE REAL CODE
               - Add cross-references to related docs
               - Maintain consistent heading structure
            
            5. Use actual Tokio code examples as the foundation for all patterns
            
            IMPORTANT: This is documentation phase - convert Context7's REAL code examples
            into pseudocode while preserving the actual patterns and best practices.
            Follow all guidelines in ms-framework-docs/CLAUDE.md
      
      - name: Improve Async Patterns Documentation (OpenAI)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'async-patterns'
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          # Create OpenAI API request for async patterns
          cat > openai_request.json << 'EOF'
          {
            "model": "o4-mini-2025-04-16",
            "messages": [
              {
                "role": "system",
                "content": "You are improving the MisterSmith framework documentation. Only work with files in the ms-framework-docs directory. Convert real code examples to pseudocode for documentation."
              },
              {
                "role": "user",
                "content": "
            You are improving the MisterSmith framework documentation for async patterns.
            
            CONTEXT7 LIBRARY REFERENCES:
            - futures v0.3.31 (/rust-lang/futures-rs) - Stream, Future combinators
            - async-trait v0.1.83 (/dtolnay/async-trait) - Async traits
            
            TASK: Improve ms-framework-docs/core-architecture/async-patterns.md by:
            
            1. Use Context7 to get REAL futures v0.3.31 code examples:
               - Search for "stream map filter"
               - Search for "future join select"
               - Search for "async error handling"
               - Search for "async trait impl"
               - Search for "future timeout"
            
            2. Take the ACTUAL code from Context7 and adapt:
               - Use real Stream and Future implementations as templates
               - Convert actual error handling patterns to pseudocode
               - Keep the real combinators and their usage patterns
               - Preserve the actual async trait patterns from the library
            
            3. Ensure alignment with supervision-trees.md for error propagation
            4. Cross-reference with agent-lifecycle.md for state machines
            
            5. Format with clear sections using REAL code structure
            
            IMPORTANT: Use Context7's actual futures-rs code examples as the base,
            then convert to documentation pseudocode while keeping real patterns."
              }
            ]
          }
          EOF
          
          # Call OpenAI API
          response=$(curl -s https://api.openai.com/v1/chat/completions \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $OPENAI_API_KEY" \
            -d @openai_request.json)
          
          # Extract content and apply changes
          echo "$response" | jq -r '.choices[0].message.content' > improvements.md
          
          # Apply improvements to the file
          if [ -s improvements.md ]; then
            # Apply the improvements to the actual file
            cp improvements.md ms-framework-docs/core-architecture/async-patterns.md
            rm improvements.md
          fi
          
          # Clean up request file
          rm -f openai_request.json
      
      - name: Improve Supervision Trees Documentation (Claude)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'supervision-trees'
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          model: "claude-sonnet-4-20250514"
          direct_prompt: |
            You are improving the MisterSmith framework supervision tree documentation.
            
            CONTEXT: Erlang-style supervision trees implemented with Tokio
            
            TASK: Improve ms-framework-docs/core-architecture/supervision-trees.md by:
            
            1. Use Context7 and web search to find REAL supervision implementations:
               - Search Context7 for "tokio task supervision"
               - Search Context7 for "error recovery patterns"
               - Search web for actual Rust supervision tree libraries (like bastion-rs)
               - Look for real restart strategy implementations
            
            2. Adapt REAL supervision code patterns:
               - Take actual supervisor trait implementations
               - Convert real restart strategies to pseudocode
               - Use actual backoff algorithms from libraries
               - Base health checks on real monitoring code
            
            3. Ensure consistency with:
               - tokio-runtime.md (task spawning)
               - agent-lifecycle.md (lifecycle states)
               - process-management-specifications.md
            
            4. Add sequence diagrams based on REAL supervision flows
            
            IMPORTANT: Use actual supervision implementations as reference,
            convert to pseudocode while preserving real architectural patterns.
      
      - name: Improve Agent Lifecycle Documentation (OpenAI)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'agent-lifecycle'
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          # Create OpenAI API request for agent lifecycle
          cat > openai_lifecycle.json << 'EOF'
          {
            "model": "o4-mini-2025-04-16",
            "messages": [
              {
                "role": "system",
                "content": "You are improving the MisterSmith framework documentation. Only work with files in the ms-framework-docs directory. Convert real code examples to pseudocode for documentation."
              },
              {
                "role": "user",
                "content": "
            You are improving the MisterSmith agent lifecycle documentation.
            
            CONTEXT7 REFERENCES:
            - crossbeam-channel v0.5.13 (/crossbeam-rs/crossbeam) - Actor communication
            
            TASK: Improve ms-framework-docs/data-management/agent-lifecycle.md by:
            
            1. Use Context7 to get REAL crossbeam v0.5.13 code:
               - Search for "crossbeam channel send recv"
               - Search for "crossbeam select"
               - Search for "bounded unbounded channel"
               - Get actual channel communication patterns
            
            2. Find REAL state machine implementations:
               - Search web for "Rust state machine libraries"
               - Look at actual FSM crates for patterns
               - Use real state transition code as reference
            
            3. Adapt the REAL code to documentation:
               - Convert actual channel usage to agent communication pseudocode
               - Base state transitions on real FSM implementations
               - Use actual event publishing patterns from libraries
            
            4. Cross-reference with:
               - agent-operations.md
               - agent-orchestration.md
               - supervision-trees.md
            
            5. Create state diagrams based on REAL state machines
            
            IMPORTANT: Use Context7's actual crossbeam examples and real FSM code,
            then convert to pseudocode preserving the actual patterns."
              }
            ]
          }
          EOF
          
          # Call OpenAI API and process response
          response=$(curl -s https://api.openai.com/v1/chat/completions \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $OPENAI_API_KEY" \
            -d @openai_lifecycle.json)
          
          # Extract and apply improvements
          echo "$response" | jq -r '.choices[0].message.content' > lifecycle_improvements.md
          
          if [ -s lifecycle_improvements.md ]; then
            # Apply the improvements to the actual file
            cp lifecycle_improvements.md ms-framework-docs/data-management/agent-lifecycle.md
            rm lifecycle_improvements.md
          fi
          
          # Clean up request file
          rm -f openai_lifecycle.json
      
      - name: Check for changes
        id: check_changes
        run: |
          # Use git status --porcelain to detect both new and modified files
          if [ -z "$(git status --porcelain)" ]; then
            echo "No changes made"
            echo "has_changes=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected:"
            git status --short
            echo "has_changes=true" >> $GITHUB_OUTPUT
            git add -A
            git commit -m "docs: Improve async runtime documentation
            
            - Enhanced Tokio runtime patterns and configuration
            - Added comprehensive async patterns with futures-rs
            - Improved supervision tree documentation
            - Updated agent lifecycle state machines
            
            Based on Context7 library documentation:
            - Tokio v1.45.0
            - futures v0.3.31
            - async-trait v0.1.83
            - crossbeam-channel v0.5.13"
          fi
      
      - name: Push changes and create PR
        if: steps.check_changes.outputs.has_changes == 'true'
        run: |
          git push origin "${{ env.BRANCH_NAME }}"
          gh pr create \
            --title "docs: Async Runtime Documentation Improvements" \
            --body "## Async Runtime Documentation Updates
            
            This PR improves the async runtime documentation based on:
            - Tokio v1.45.0 best practices
            - futures-rs patterns
            - Supervision tree patterns
            - Agent lifecycle management
            
            ### Changes
            - Enhanced pseudocode examples
            - Added comprehensive patterns
            - Improved cross-references
            - Ensured version consistency
            
            ### Context7 References
            - Tokio: /tokio-rs/tokio
            - Futures: /rust-lang/futures-rs
            - Async-trait: /dtolnay/async-trait
            - Crossbeam: /crossbeam-rs/crossbeam
            
            Generated by Async Runtime Documentation workflow" \
            --base main \
            --head "${{ env.BRANCH_NAME }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}