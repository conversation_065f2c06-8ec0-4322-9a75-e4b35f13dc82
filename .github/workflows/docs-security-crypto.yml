name: Documentation - Security & Cryptography

on:
  workflow_dispatch:
    inputs:
      focus_area:
        description: 'Specific security area to focus on'
        required: false
        type: choice
        options:
          - 'all'
          - 'authentication'
          - 'authorization'
          - 'encryption'
          - 'security-patterns'
        default: 'all'
  schedule:
    # Run daily at 6 AM UTC
    - cron: '0 6 * * *'
  push:
    paths:
      - 'ms-framework-docs/security/*.md'

jobs:
  improve-security-docs:
    name: Improve Security Documentation
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Setup branch
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          BRANCH_NAME="docs/security-improvements-$(date +%Y%m%d-%H%M%S)"
          git checkout -b "$BRANCH_NAME"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
      
      - name: Improve Authentication Documentation (OpenAI)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'authentication'
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          # Create OpenAI request for authentication documentation
          cat > auth_request.json << 'EOF'
          {
            "model": "o4-mini-2025-04-16",
            "messages": [
              {
                "role": "system",
                "content": "You are improving the MisterSmith framework authentication documentation. Only work with files in ms-framework-docs directory."
              },
              {
                "role": "user",
                "content": "Improve ms-framework-docs/security/authentication-implementation.md by:\n\n1. Use Context7 to get REAL jwt-simple v0.12.10 code:\n   - Search for 'jwt encode decode'\n   - Search for 'jwt claims'\n   - Search for 'jwt rsa ecdsa'\n   - Get actual JWT implementation examples\n\n2. Take the REAL JWT code and adapt:\n   - Convert actual token generation to pseudocode\n   - Use real key management from examples\n   - Base refresh on actual JWT patterns\n   - Keep real validation logic\n\n3. Search web for actual JWT libraries and security guides\n\n4. Ensure consistency with:\n   - authorization-implementation.md\n   - security-framework.md\n   - All transport layers (HTTP headers, gRPC metadata)\n\n5. Use REAL security patterns from production code\n\nIMPORTANT: Use Context7's actual jwt-simple examples, convert to pseudocode preserving real security patterns."
              }
            ],
            "max_completion_tokens": 4000
          }
          EOF
          
          # Call OpenAI API
          response=$(curl -s https://api.openai.com/v1/chat/completions \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $OPENAI_API_KEY" \
            -d @auth_request.json)
          
          # Check for errors
          if echo "$response" | jq -e '.error' > /dev/null; then
            echo "Error from OpenAI API:"
            echo "$response" | jq '.error'
            exit 1
          fi
          
          # Extract content
          echo "$response" | jq -r '.choices[0].message.content' > auth_improvements.md
          echo "OpenAI improvements for authentication documentation generated"
      
      - name: Improve Authorization Documentation (Claude)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'authorization'
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          model: "claude-sonnet-4-20250514"
          direct_prompt: |
            You are improving the MisterSmith framework authorization documentation.
            
            TASK: Improve ms-framework-docs/security/authorization-implementation.md and authorization-specifications.md by:
            
            1. Find REAL authorization code:
               - Search web for actual RBAC/ABAC Rust libraries
               - Look for casbin-rs, oso, or similar
               - Find real permission checking code
            
            2. Adapt REAL authorization patterns:
               - Use actual RBAC implementations
               - Take ABAC rules from real systems
               - Base policies on actual engines
               - Keep real permission evaluation
               - Use actual audit logging code
            
            3. Define schemas from REAL systems:
               - Permission models from actual code
               - Role hierarchies from production
               - Policy syntax from real engines
               - Context from actual middleware
            
            4. Cross-reference with:
               - authentication-implementation.md
               - All agent operation files
               - Transport middleware integration
            
            IMPORTANT: Use real authorization libraries as reference,
            convert to pseudocode preserving actual patterns.
      
      - name: Improve Encryption Documentation (OpenAI)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'encryption'
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          # Create OpenAI request for encryption documentation
          cat > encryption_request.json << 'EOF'
          {
            "model": "o4-mini-2025-04-16",
            "messages": [
              {
                "role": "system",
                "content": "You are improving the MisterSmith framework encryption documentation. Only work with files in ms-framework-docs directory."
              },
              {
                "role": "user",
                "content": "Create/improve encryption documentation by:\n\n1. Use Context7 to get REAL ring v0.17.8 code:\n   - Search for 'ring aead'\n   - Search for 'ring pbkdf2'\n   - Search for 'ring rand'\n   - Get actual crypto examples\n\n2. If needed, create ms-framework-docs/security/encryption-patterns.md\n3. Take the REAL crypto code and adapt:\n   - Convert actual AES-GCM usage to pseudocode\n   - Use real KDF implementations\n   - Base key rotation on actual patterns\n   - Keep real random generation\n   - Use actual TLS configs\n\n4. Search web for actual key management systems\n\n5. Cross-reference with:\n   - All transport layers (TLS config)\n   - Database encryption\n   - Message encryption patterns\n\nIMPORTANT: Use Context7's actual ring examples, convert to safe pseudocode patterns."
              }
            ],
            "max_completion_tokens": 4000
          }
          EOF
          
          # Call OpenAI API
          response=$(curl -s https://api.openai.com/v1/chat/completions \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $OPENAI_API_KEY" \
            -d @encryption_request.json)
          
          # Extract content and potentially create file
          content=$(echo "$response" | jq -r '.choices[0].message.content')
          
          # Check if encryption-patterns.md exists
          if [ ! -f "ms-framework-docs/security/encryption-patterns.md" ]; then
            echo "Creating encryption-patterns.md"
            echo "$content" > ms-framework-docs/security/encryption-patterns.md
          else
            echo "$content" > encryption_improvements.md
          fi
          
          echo "OpenAI improvements for encryption documentation generated"
      
      - name: Improve Security Patterns Documentation (Claude)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'security-patterns'
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          model: "claude-sonnet-4-20250514"
          direct_prompt: |
            You are improving the MisterSmith security patterns documentation.
            
            TASK: Improve ms-framework-docs/security/security-patterns.md by:
            
            1. Find REAL security implementations:
               - Search for actual zero trust Rust code
               - Look for real validation libraries
               - Find actual rate limiting implementations
               - Get real security header configs
            
            2. Adapt REAL security code:
               - Use actual input validation patterns
               - Take CSRF protection from real frameworks
               - Base rate limiting on actual libraries
               - Keep real header configurations
               - Use actual secrets management code
            
            3. Include REAL threat mitigations:
               - Attack vectors from actual CVEs
               - Mitigations from real patches
               - Boundaries from actual architectures
               - Trust zones from production systems
            
            4. Cross-reference with:
               - security-framework.md
               - security-integration.md
               - All transport and data layers
            
            5. Create checklists from REAL security audits
            
            IMPORTANT: Use real security code as reference,
            convert to documentation preserving actual defenses.
      
      - name: Apply OpenAI improvements
        if: github.event.inputs.focus_area == 'all' || contains('authentication,encryption', github.event.inputs.focus_area)
        run: |
          echo "=== Applying OpenAI Improvements ==="
          
          # Apply authentication improvements
          if [ -f auth_improvements.md ]; then
            echo "Applying authentication improvements"
            cp auth_improvements.md ms-framework-docs/security/authentication-implementation.md
            rm auth_improvements.md
          fi
          
          # Apply encryption improvements
          if [ -f encryption_improvements.md ]; then
            echo "Applying encryption improvements"
            if [ -f ms-framework-docs/security/encryption-patterns.md ]; then
              cp encryption_improvements.md ms-framework-docs/security/encryption-patterns.md
            fi
            rm encryption_improvements.md
          fi
          
          # Clean up request files
          rm -f auth_request.json encryption_request.json
      
      - name: Check for changes
        id: check_changes
        run: |
          if [ -z "$(git status --porcelain)" ]; then
            echo "No changes made"
            echo "has_changes=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected:"
            git status --short
            echo "has_changes=true" >> $GITHUB_OUTPUT
            git add -A
            git commit -m "docs: Improve security & cryptography documentation
            
            - Enhanced authentication with JWT patterns (OpenAI)
            - Added comprehensive authorization models (Claude)
            - Improved encryption and key management (OpenAI)
            - Updated security patterns and threat models (Claude)
            
            Based on Context7 library documentation:
            - jwt-simple v0.12.10
            - ring v0.17.8
            - aes-gcm v0.10.3
            - chacha20poly1305 v0.10.1
            
            Models used:
            - OpenAI o4-mini-2025-04-16 for authentication and encryption
            - Claude Sonnet 4 for authorization and security patterns"
          fi
      
      - name: Push changes and create PR
        if: steps.check_changes.outputs.has_changes == 'true'
        run: |
          git push origin "${{ env.BRANCH_NAME }}"
          gh pr create \
            --title "docs: Security & Cryptography Documentation Improvements" \
            --body "## Security Documentation Updates
            
            This PR improves the security documentation based on:
            - jwt-simple v0.12.10 (JWT authentication)
            - ring v0.17.8 (cryptographic primitives)
            - Modern security patterns and threat models
            
            ### Changes
            - Enhanced authentication flows and patterns
            - Added authorization models (RBAC/ABAC)
            - Improved encryption and key management
            - Updated security patterns and checklists
            
            ### Models Used
            - **OpenAI o4-mini-2025-04-16**: Authentication and encryption patterns
            - **Claude Sonnet 4**: Authorization models and security patterns
            
            ### Context7 References
            - jwt-simple: /rafaelbarbosa/jwt-simple
            - ring: /briansmith/ring
            
            ### Security Considerations
            - All examples use pseudocode only
            - No sensitive implementation details
            - Focus on patterns and best practices
            
            Generated by Security Documentation workflow" \
            --base main \
            --head "${{ env.BRANCH_NAME }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}