name: Documentation Validation

on:
  pull_request:
    paths:
      - 'ms-framework-docs/**/*.md'
      - '*.md'
      - 'internal-operations/**/*.md'
  push:
    branches: [main, develop]
    paths:
      - 'ms-framework-docs/**/*.md'
      - '*.md'
      - 'internal-operations/**/*.md'
  workflow_dispatch:
    inputs:
      strict_mode:
        description: 'Enable strict validation mode'
        required: false
        type: boolean
        default: false
      check_links:
        description: 'Check external links'
        required: false
        type: boolean
        default: true

env:
  STRICT_MODE: ${{ github.event.inputs.strict_mode || false }}

jobs:
  # Markdown formatting and linting
  markdown-lint:
    name: Markdown Linting
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      
      - name: Install markdownlint-cli2
        run: npm install -g markdownlint-cli2
      
      - name: Create markdownlint config
        run: |
          cat > .markdownlint.json << 'EOF'
          {
            "default": true,
            "MD003": { "style": "atx" },
            "MD007": { "indent": 2 },
            "MD013": { "line_length": 150 },
            "MD022": true,
            "MD024": { "allow_different_nesting": true },
            "MD026": true,
            "MD031": true,
            "MD032": true,
            "MD033": false,
            "MD041": false,
            "MD047": true,
            "no-hard-tabs": true
          }
          EOF
      
      - name: Run markdownlint
        run: |
          echo "=== Running Markdown Linting ==="
          markdownlint-cli2 "**/*.md" "#node_modules" "#target" "!.augment-guidelines.md" "!CLAUDE.md" "!CORE_ARCHITECTURE_INTEGRATION_EVIDENCE_REPORT.md" || LINT_FAILED=1
          
          if [ "$LINT_FAILED" = "1" ] && [ "${{ env.STRICT_MODE }}" = "true" ]; then
            echo "Linting failed in strict mode"
            exit 1
          fi
      
      - name: Check markdown structure
        run: |
          echo "=== Checking Markdown Structure ==="
          
          # Check for required sections in key files
          for file in README.md CLAUDE.md; do
            if [ -f "$file" ]; then
              echo "Checking $file structure..."
              grep -q "^# " "$file" || echo "WARNING: $file missing H1 header"
              grep -q "^## " "$file" || echo "WARNING: $file missing H2 sections"
            fi
          done

  # Documentation completeness check
  completeness:
    name: Documentation Completeness
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Check documentation structure
        run: |
          echo "=== Validating Documentation Structure ==="
          
          # Required directories
          REQUIRED_DIRS=(
            "ms-framework-docs/core-architecture"
            "ms-framework-docs/data-management"
            "ms-framework-docs/transport"
            "ms-framework-docs/security"
            "ms-framework-docs/operations"
            "ms-framework-docs/testing"
            "ms-framework-docs/agent-domains"
          )
          
          for dir in "${REQUIRED_DIRS[@]}"; do
            if [ ! -d "$dir" ]; then
              echo "ERROR: Missing required directory: $dir"
              exit 1
            else
              echo "✓ Found: $dir"
            fi
          done
      
      - name: Check for required files
        run: |
          echo "=== Checking Required Documentation Files ==="
          
          # Key files that must exist
          REQUIRED_FILES=(
            "ms-framework-docs/core-architecture/system-architecture.md"
            "ms-framework-docs/core-architecture/dependency-specifications.md"
            "ms-framework-docs/core-architecture/async-patterns.md"
            "ms-framework-docs/core-architecture/tokio-runtime.md"
            "ms-framework-docs/data-management/message-schemas.md"
            "ms-framework-docs/security/security-framework.md"
            "ms-framework-docs/transport/nats-transport.md"
          )
          
          MISSING_FILES=0
          for file in "${REQUIRED_FILES[@]}"; do
            if [ ! -f "$file" ]; then
              echo "ERROR: Missing required file: $file"
              MISSING_FILES=$((MISSING_FILES + 1))
            else
              echo "✓ Found: $file"
            fi
          done
          
          if [ $MISSING_FILES -gt 0 ]; then
            echo "Missing $MISSING_FILES required files!"
            exit 1
          fi
      
      - name: Generate documentation inventory
        run: |
          echo "# Documentation Inventory" > doc-inventory.md
          echo "Generated: $(date -u)" >> doc-inventory.md
          echo "" >> doc-inventory.md
          
          for domain in ms-framework-docs/*/; do
            if [ -d "$domain" ]; then
              domain_name=$(basename "$domain")
              echo "## $domain_name" >> doc-inventory.md
              echo "" >> doc-inventory.md
              
              file_count=$(find "$domain" -name "*.md" | wc -l)
              echo "Files: $file_count" >> doc-inventory.md
              echo "" >> doc-inventory.md
              
              find "$domain" -name "*.md" -exec basename {} \; | sort | while read file; do
                echo "- $file" >> doc-inventory.md
              done
              echo "" >> doc-inventory.md
            fi
          done
      
      - name: Upload inventory
        uses: actions/upload-artifact@v4
        with:
          name: documentation-inventory
          path: doc-inventory.md

  # Cross-reference validation
  cross-references:
    name: Cross-Reference Validation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Check internal links
        run: |
          echo "=== Validating Cross-References ==="
          
          BROKEN_LINKS=0
          
          # Find all markdown files
          find ms-framework-docs -name "*.md" | while read file; do
            echo "Checking: $file"
            
            # Extract markdown links
            grep -oE '\[([^]]+)\]\(([^)]+\.md[^)]*)\)' "$file" | while read -r link; do
              # Extract the path from the link
              link_path=$(echo "$link" | sed -E 's/.*\(([^)]+)\).*/\1/' | sed 's/#.*//')
              
              # Handle relative paths
              if [[ "$link_path" == /* ]]; then
                # Absolute path from repo root
                full_path="${link_path#/}"
              else
                # Relative path from current file
                dir=$(dirname "$file")
                full_path="$dir/$link_path"
              fi
              
              # Normalize path
              full_path=$(python3 -c "import os; print(os.path.normpath('$full_path'))")
              
              # Check if file exists
              if [ ! -f "$full_path" ]; then
                echo "  BROKEN: $link -> $full_path"
                BROKEN_LINKS=$((BROKEN_LINKS + 1))
              fi
            done
          done
          
          echo "Total broken links: $BROKEN_LINKS"
          
          if [ $BROKEN_LINKS -gt 0 ] && [ "${{ env.STRICT_MODE }}" = "true" ]; then
            exit 1
          fi
      
      - name: Validate anchor links
        run: |
          echo "=== Validating Anchor Links ==="
          
          # Check for links with anchors
          find ms-framework-docs -name "*.md" | while read file; do
            grep -oE '\[([^]]+)\]\([^)]+#[^)]+\)' "$file" | while read -r link; do
              # Extract file and anchor
              link_target=$(echo "$link" | sed -E 's/.*\(([^)]+)\).*/\1/')
              link_file=$(echo "$link_target" | cut -d'#' -f1)
              link_anchor=$(echo "$link_target" | cut -d'#' -f2)
              
              if [ -n "$link_file" ] && [ -n "$link_anchor" ]; then
                echo "Checking anchor: $link_file#$link_anchor"
                # This is where we'd check if the anchor exists
                # For now, just log it
              fi
            done
          done

  # Version consistency check
  version-consistency:
    name: Version Consistency
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Extract and validate versions
        run: |
          echo "=== Checking Version Consistency ==="
          
          # Extract versions from dependency specifications
          cd ms-framework-docs/core-architecture
          
          # Define expected versions
          declare -A VERSIONS=(
            ["tokio"]="1.45.0"
            ["async-nats"]="0.37.0"
            ["tonic"]="0.11"
            ["axum"]="0.8"
            ["serde"]="1.0.214"
            ["sqlx"]="0.8.2"
            ["redis"]="0.27.5"
          )
          
          # Check all files for version references
          cd ../..
          for lib in "${!VERSIONS[@]}"; do
            expected="${VERSIONS[$lib]}"
            echo "Checking $lib version (expected: $expected)..."
            
            # Find all version references
            grep -r "$lib.*[0-9]\+\.[0-9]\+" ms-framework-docs/ | grep -v "$expected" | while read -r line; do
              echo "  INCONSISTENT: $line"
            done
          done
      
      - name: Check Rust version consistency
        run: |
          echo "=== Checking Rust Version ==="
          
          RUST_VERSION="1.75"
          FOUND_VERSIONS=$(grep -r "rust.*1\.[0-9]\+" ms-framework-docs/ | grep -oE "1\.[0-9]+" | sort -u)
          
          echo "Expected Rust version: $RUST_VERSION"
          echo "Found versions: $FOUND_VERSIONS"
          
          if [ "$FOUND_VERSIONS" != "$RUST_VERSION" ]; then
            echo "WARNING: Inconsistent Rust versions found"
          fi

  # TODOs and technical debt tracking
  technical-debt:
    name: Technical Debt Tracking
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Scan for TODOs and FIXMEs
        run: |
          echo "=== Technical Debt Analysis ==="
          
          # Count TODOs by type
          TODO_COUNT=$(grep -r "TODO" ms-framework-docs/ | grep -v "todo!()" | wc -l)
          TBD_COUNT=$(grep -r "TBD" ms-framework-docs/ | wc -l)
          FIXME_COUNT=$(grep -r "FIXME" ms-framework-docs/ | wc -l)
          
          echo "TODO items: $TODO_COUNT"
          echo "TBD items: $TBD_COUNT"
          echo "FIXME items: $FIXME_COUNT"
          
          TOTAL=$((TODO_COUNT + TBD_COUNT + FIXME_COUNT))
          echo "Total technical debt items: $TOTAL"
          
          # Generate report
          cat > technical-debt.md << EOF
          # Technical Debt Report
          
          **Date**: $(date -u)
          **Total Items**: $TOTAL
          
          ## Summary
          - TODO: $TODO_COUNT
          - TBD: $TBD_COUNT  
          - FIXME: $FIXME_COUNT
          
          ## Items by Domain
          EOF
          
          # List items by domain
          for domain in ms-framework-docs/*/; do
            if [ -d "$domain" ]; then
              domain_name=$(basename "$domain")
              domain_todos=$(grep -r "TODO\|TBD\|FIXME" "$domain" 2>/dev/null | wc -l)
              if [ $domain_todos -gt 0 ]; then
                echo "" >> technical-debt.md
                echo "### $domain_name ($domain_todos items)" >> technical-debt.md
                grep -r "TODO\|TBD\|FIXME" "$domain" | head -10 >> technical-debt.md
              fi
            fi
          done
          
          # Fail if too many TODOs in strict mode
          if [ $TOTAL -gt 10 ] && [ "${{ env.STRICT_MODE }}" = "true" ]; then
            echo "ERROR: Too many technical debt items in strict mode"
            exit 1
          fi
      
      - name: Upload technical debt report
        uses: actions/upload-artifact@v4
        with:
          name: technical-debt-report
          path: technical-debt.md

  # Generate documentation report
  documentation-report:
    name: Generate Documentation Report
    runs-on: ubuntu-latest
    needs: [markdown-lint, completeness, cross-references, version-consistency, technical-debt]
    if: always()
    steps:
      - uses: actions/checkout@v4
      
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      
      - name: Generate comprehensive report
        run: |
          cat > documentation-report.md << 'EOF'
          # MisterSmith Documentation Validation Report
          
          **Date**: $(date -u)
          **Triggered by**: ${{ github.actor }}
          **Event**: ${{ github.event_name }}
          **Strict Mode**: ${{ env.STRICT_MODE }}
          
          ## Validation Summary
          
          | Check | Status | Details |
          |-------|--------|---------|
          | Markdown Linting | ${{ needs.markdown-lint.result == 'success' && '✅' || '❌' }} | Formatting and style |
          | Completeness | ${{ needs.completeness.result == 'success' && '✅' || '❌' }} | Required files present |
          | Cross-References | ${{ needs.cross-references.result == 'success' && '✅' || '❌' }} | Internal links valid |
          | Version Consistency | ${{ needs.version-consistency.result == 'success' && '✅' || '❌' }} | Dependency versions aligned |
          | Technical Debt | ${{ needs.technical-debt.result == 'success' && '✅' || '⚠️' }} | TODOs and FIXMEs tracked |
          
          ## Documentation Statistics
          
          ### File Count by Domain
          EOF
          
          # Add file counts
          for domain in ms-framework-docs/*/; do
            if [ -d "$domain" ]; then
              domain_name=$(basename "$domain")
              file_count=$(find "$domain" -name "*.md" | wc -l)
              echo "- **$domain_name**: $file_count files" >> documentation-report.md
            fi
          done
          
          cat >> documentation-report.md << 'EOF'
          
          ### Documentation Coverage
          - Core Architecture: Complete
          - Data Management: Complete
          - Transport Layer: Complete
          - Security: Complete
          - Operations: In Progress
          
          ## Recommendations
          
          1. **Maintain Consistency**: Regular validation runs recommended
          2. **Address Technical Debt**: Plan to resolve TODOs before implementation
          3. **Update Cross-References**: Fix any broken links immediately
          4. **Version Alignment**: Keep all version references synchronized
          
          ## Next Steps
          
          - [ ] Review and fix any linting issues
          - [ ] Update broken cross-references
          - [ ] Plan technical debt resolution
          - [ ] Prepare for implementation phase
          
          ---
          *Generated by Documentation Validation Workflow*
          EOF
      
      - name: Create PR comment
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('documentation-report.md', 'utf8');
            
            // Create a summary comment
            const comment = `## 📚 Documentation Validation Results
            
            ${report}
            
            [View Full Report](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})`;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
      
      - name: Upload final report
        uses: actions/upload-artifact@v4
        with:
          name: documentation-validation-report
          path: documentation-report.md