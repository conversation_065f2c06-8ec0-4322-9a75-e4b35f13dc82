name: Documentation - Transport & Messaging

on:
  workflow_dispatch:
    inputs:
      focus_area:
        description: 'Specific transport layer to focus on'
        required: false
        type: choice
        options:
          - 'all'
          - 'nats-messaging'
          - 'grpc-tonic'
          - 'http-axum'
          - 'message-schemas'
        default: 'all'
  schedule:
    # Run daily at 4 AM UTC
    - cron: '0 4 * * *'
  push:
    paths:
      - 'ms-framework-docs/transport/*.md'
      - 'ms-framework-docs/data-management/*message*.md'

jobs:
  improve-transport-docs:
    name: Improve Transport Documentation
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      id-token: write
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Setup branch
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          BRANCH_NAME="docs/transport-improvements-$(date +%Y%m%d-%H%M%S)"
          git checkout -b "$BRANCH_NAME"
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
      
      - name: Improve NATS Transport Documentation (OpenAI)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'nats-messaging'
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          # Create OpenAI request for NATS documentation
          cat > nats_request.json << 'EOF'
          {
            "model": "o4-mini-2025-04-16",
            "messages": [
              {
                "role": "system",
                "content": "You are improving the MisterSmith framework NATS transport documentation. Only work with files in ms-framework-docs directory."
              },
              {
                "role": "user",
                "content": "Improve ms-framework-docs/transport/nats-transport.md by:\n\n1. Use Context7 to get REAL async-nats v0.37.0 code:\n   - Search for 'jetstream publish subscribe'\n   - Search for 'nats kv get put'\n   - Search for 'object store'\n   - Search for 'service api'\n   - Get actual NATS client usage examples\n\n2. Take the REAL NATS code and adapt it:\n   - Convert actual JetStream setup code to pseudocode\n   - Use real consumer patterns from the library\n   - Base KV operations on actual async-nats examples\n   - Keep real error handling patterns\n\n3. Ensure consistency with:\n   - message-schemas.md (message formats)\n   - agent-communication.md (communication patterns)\n   - connection-management.md\n\n4. Use REAL reconnection code from async-nats\n5. Base performance tips on actual library docs\n\nIMPORTANT: Use Context7's actual async-nats code examples, then convert to pseudocode while preserving real NATS patterns."
              }
            ],
            "max_completion_tokens": 4000
          }
          EOF
          
          # Call OpenAI API
          response=$(curl -s https://api.openai.com/v1/chat/completions \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $OPENAI_API_KEY" \
            -d @nats_request.json)
          
          # Check for errors
          if echo "$response" | jq -e '.error' > /dev/null; then
            echo "Error from OpenAI API:"
            echo "$response" | jq '.error'
            exit 1
          fi
          
          # Extract content
          echo "$response" | jq -r '.choices[0].message.content' > nats_improvements.md
          echo "OpenAI improvements for NATS transport documentation generated"
      
      - name: Improve gRPC/Tonic Documentation (Claude)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'grpc-tonic'
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          model: "claude-sonnet-4-20250514"
          direct_prompt: |
            You are improving the MisterSmith framework gRPC transport documentation.
            
            CONTEXT7 LIBRARY REFERENCES:
            - tonic v0.11 (/hyperium/tonic) - gRPC framework
            - Features: ["full"]
            
            TASK: Improve ms-framework-docs/transport/grpc-transport.md by:
            
            1. Use Context7 to get REAL Tonic v0.11 code:
               - Search for "tonic server example"
               - Search for "tonic client"
               - Search for "tonic interceptor"
               - Search for "tonic streaming"
               - Get actual gRPC service implementations
            
            2. Take the REAL Tonic code and adapt:
               - Convert actual service traits to pseudocode
               - Use real interceptor patterns from examples
               - Base streaming on actual Tonic stream handlers
               - Keep real error handling and status codes
            
            3. Cross-reference with:
               - authentication-implementation.md
               - authorization-implementation.md  
               - integration-contracts.md
            
            4. Use REAL proto patterns from Tonic examples
            
            IMPORTANT: Use Context7's actual Tonic examples,
            convert to pseudocode preserving real gRPC patterns.
      
      - name: Improve HTTP/Axum Documentation (OpenAI)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'http-axum'
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          # Create OpenAI request for Axum documentation
          cat > axum_request.json << 'EOF'
          {
            "model": "o4-mini-2025-04-16",
            "messages": [
              {
                "role": "system",
                "content": "You are improving the MisterSmith framework HTTP transport documentation. Only work with files in ms-framework-docs directory."
              },
              {
                "role": "user",
                "content": "Improve ms-framework-docs/transport/http-transport.md by:\n\n1. Use Context7 to get REAL Axum v0.8 code:\n   - Search for 'axum router'\n   - Search for 'axum middleware'\n   - Search for 'axum websocket'\n   - Search for 'axum state'\n   - Search for 'axum extractors'\n\n2. Take the REAL Axum code and adapt:\n   - Convert actual router setup to pseudocode\n   - Use real middleware chains from examples\n   - Base WebSocket on actual upgrade handlers\n   - Keep real extractor patterns\n   - Use actual error handling from Axum\n\n3. Ensure consistency with:\n   - security-framework.md (auth middleware)\n   - observability-monitoring-framework.md (metrics/tracing)\n\n4. Use REAL REST patterns from Axum examples\n\nIMPORTANT: Use Context7's actual Axum examples, convert to pseudocode preserving real HTTP patterns."
              }
            ],
            "max_completion_tokens": 4000
          }
          EOF
          
          # Call OpenAI API
          response=$(curl -s https://api.openai.com/v1/chat/completions \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $OPENAI_API_KEY" \
            -d @axum_request.json)
          
          # Extract content
          echo "$response" | jq -r '.choices[0].message.content' > axum_improvements.md
          echo "OpenAI improvements for HTTP/Axum documentation generated"
      
      - name: Improve Message Schemas Documentation (Claude)
        if: github.event.inputs.focus_area == 'all' || github.event.inputs.focus_area == 'message-schemas'
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          model: "claude-sonnet-4-20250514"
          direct_prompt: |
            You are improving the MisterSmith message schema documentation.
            
            CONTEXT7 LIBRARY REFERENCES:
            - serde v1.0.214 (/serde-rs/serde) - Serialization
            - serde_json v1.0.132 - JSON support
            
            TASK: Improve ms-framework-docs/data-management/message-schemas.md by:
            
            1. Use Context7 to get REAL Serde v1.0.214 code:
               - Search for "serde derive serialize"
               - Search for "serde custom serialization"
               - Search for "serde flatten"
               - Search for "serde rename"
               - Get actual serialization examples
            
            2. Take the REAL Serde code and adapt:
               - Convert actual derive macros to documentation
               - Use real attribute patterns from examples
               - Base versioning on actual Serde techniques
               - Keep real validation patterns
            
            3. Cross-reference and ensure consistency:
               - core-message-schemas.md
               - system-message-schemas.md
               - workflow-message-schemas.md
               - All transport layers (NATS, gRPC, HTTP)
            
            4. Use REAL Serde attributes from examples
            
            IMPORTANT: Use Context7's actual Serde examples,
            convert to pseudocode preserving real patterns.
      
      - name: Apply OpenAI improvements
        if: github.event.inputs.focus_area == 'all' || contains('nats-messaging,http-axum', github.event.inputs.focus_area)
        run: |
          echo "=== Applying OpenAI Improvements ==="
          
          # Apply NATS improvements if available
          if [ -f nats_improvements.md ]; then
            echo "Applying NATS Transport improvements"
            cp nats_improvements.md ms-framework-docs/transport/nats-transport.md
            rm nats_improvements.md
          fi
          
          # Apply Axum improvements if available
          if [ -f axum_improvements.md ]; then
            echo "Applying HTTP/Axum improvements"
            cp axum_improvements.md ms-framework-docs/transport/http-transport.md
            rm axum_improvements.md
          fi
          
          # Clean up request files
          rm -f nats_request.json axum_request.json
      
      - name: Check for changes
        id: check_changes
        run: |
          if [ -z "$(git status --porcelain)" ]; then
            echo "No changes made"
            echo "has_changes=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected:"
            git status --short
            echo "has_changes=true" >> $GITHUB_OUTPUT
            git add -A
            git commit -m "docs: Improve transport & messaging documentation
            
            - Enhanced NATS transport with JetStream patterns (OpenAI)
            - Added comprehensive gRPC/Tonic examples (Claude)
            - Improved HTTP/Axum patterns and middleware (OpenAI)
            - Updated message schemas with Serde patterns (Claude)
            
            Based on Context7 library documentation:
            - async-nats v0.37.0
            - tonic v0.11
            - axum v0.8
            - serde v1.0.214
            
            Models used:
            - OpenAI o4-mini-2025-04-16 for NATS and Axum
            - Claude Sonnet 4 for gRPC and Serde"
          fi
      
      - name: Push changes and create PR
        if: steps.check_changes.outputs.has_changes == 'true'
        run: |
          git push origin "${{ env.BRANCH_NAME }}"
          gh pr create \
            --title "docs: Transport & Messaging Documentation Improvements" \
            --body "## Transport Layer Documentation Updates
            
            This PR improves the transport and messaging documentation based on:
            - async-nats v0.37.0 (JetStream, KV, Service discovery)
            - Tonic v0.11 (gRPC patterns)
            - Axum v0.8 (HTTP/WebSocket)
            - Serde v1.0.214 (Message schemas)
            
            ### Changes
            - Enhanced transport patterns for each protocol
            - Added comprehensive pseudocode examples
            - Improved message schema definitions
            - Ensured cross-layer consistency
            
            ### Models Used
            - **OpenAI o4-mini-2025-04-16**: NATS and HTTP/Axum documentation
            - **Claude Sonnet 4**: gRPC/Tonic and message schemas
            
            ### Context7 References
            - NATS: /nats-io/nats.rs
            - Tonic: /hyperium/tonic
            - Axum: /tokio-rs/axum
            - Serde: /serde-rs/serde
            
            Generated by Transport Documentation workflow" \
            --base main \
            --head "${{ env.BRANCH_NAME }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}